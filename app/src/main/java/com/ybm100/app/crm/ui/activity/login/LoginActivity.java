package com.ybm100.app.crm.ui.activity.login;

import static com.ybm100.app.crm.constant.GlobalConstants.TOUCH_TIME;
import static com.ybm100.app.crm.constant.GlobalConstants.WAIT_TIME;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Bundle;
import android.os.Process;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.quick.qt.analytics.QtTrackAgent;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.umeng.message.PushAgent;
import com.umeng.message.UTrack;
import com.xyy.common.util.DeviceUtils;
import com.xyy.common.util.ToastUtils;
import com.xyy.flutter.container.container.ContainerRuntime;
import com.xyy.utilslibrary.AppManager;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity;
import com.xyy.utilslibrary.utils.ColorUtils;
import com.xyy.utilslibrary.utils.DeviceUuidFactory;
import com.xyy.utilslibrary.utils.ImageUtils;
import com.xyy.utilslibrary.utils.ViewUtils;
import com.ybm100.app.crm.BuildConfig;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.CaptchaBean;
import com.ybm100.app.crm.bean.user.UserInfoBean;
import com.ybm100.app.crm.constant.AppNetConfig;
import com.ybm100.app.crm.contract.login.LoginContract;
import com.ybm100.app.crm.doraemon.SettingUrlActivity;
import com.ybm100.app.crm.permission.PermissionUtil;
import com.ybm100.app.crm.platform.RuntimeEnv;
import com.ybm100.app.crm.presenter.login.LoginPresenter;
import com.ybm100.app.crm.ui.activity.personal.NetworkDiagnosticActivity;
import com.ybm100.app.crm.utils.InputFilter.EditUtil;
import com.ybm100.app.crm.utils.MultipleClickUtil;
import com.ybm100.app.crm.utils.SharedPrefManager;
import com.ybm100.app.crm.utils.SnowGroundUtils;
import com.ybm100.app.crm.widget.EditTextWithDel;

import java.util.HashMap;
import java.util.UUID;

import butterknife.BindView;
import butterknife.OnClick;
import ly.count.android.sdk.XyyApmCly;

import com.quick.qt.commonsdk.QtConfigure;


/**
 * 登录
 */
public class LoginActivity extends BaseMVPCompatActivity<LoginPresenter> implements LoginContract.ILoginView {

    @BindView(R.id.et_login_phone)
    EditTextWithDel etLoginPhone;
    @BindView(R.id.et_login_pwd)
    EditTextWithDel etLoginPwd;
    @BindView(R.id.et_verify_code)
    EditTextWithDel etVerifyCode;
    @BindView(R.id.tv_login_button)
    TextView tvLoginButton;
    @BindView(R.id.tv_forget_pwd)
    TextView tvForgetPwd;
    @BindView(R.id.im_show_pwd)
    ImageView imShowPwd;
    @BindView(R.id.tv_status)
    TextView tvStatus;
    @BindView(R.id.im_verify_code)
    ImageView imVerifyCode;
    @BindView(R.id.progress_bar)
    ProgressBar progressBar;
    @BindView(R.id.iv_logo)
    ImageView ivLogo;
    private String captchaId;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_login;
    }

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return LoginPresenter.newInstance();
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        /**
         * 移除友盟推送Alias
         */
        deletePushAlias();

        // 清空上次登录的数据
        SharedPrefManager.getInstance().logout();
        addListener();
        if (!AppNetConfig.FlavorType.PROD.equals(BuildConfig.FLAVOR)) {
            tvStatus.setVisibility(View.VISIBLE);
            String currFlavor = SharedPrefManager.getInstance().getCurrFlavor();
            tvStatus.setText(TextUtils.isEmpty(currFlavor) ? BuildConfig.FLAVOR : currFlavor);
        } else {
            tvStatus.setVisibility(View.GONE);
        }
        mPresenter.getVerifyCode();
        progressBar.setVisibility(View.VISIBLE);
        imVerifyCode.setEnabled(false);

        /**
         * 网络诊断工具
         */
        MultipleClickUtil.startActivityOnMultipleClick(ivLogo, this, NetworkDiagnosticActivity.class, 5);

        SnowGroundUtils.identify("", null);
    }

    private void deletePushAlias() {
        PushAgent mPushAgent = PushAgent.getInstance(getApplicationContext());

        String oaId = "";
        UserInfoBean infoBean = SharedPrefManager.getInstance().getUserInfo();
        if (infoBean != null) {
            oaId = infoBean.getSysUserId();
        }

        mPushAgent.deleteAlias(oaId, "CRMACCOUNT", new UTrack.ICallBack() {

            @Override
            public void onMessage(boolean isSuccess, String message) {

            }

        });
    }

    private boolean isAppProd() {
        String currFlavor = SharedPrefManager.getInstance().getCurrFlavor();
        if (TextUtils.isEmpty(currFlavor) || AppNetConfig.FlavorType.CUSTOM.equals(currFlavor)) {
            currFlavor = RuntimeEnv.INSTANCE.getEnv();
        }
        return AppNetConfig.FlavorType.PROD.equalsIgnoreCase(currFlavor);
    }

    /**
     * 添加监听
     */
    private void addListener() {
        imShowPwd.setSelected(false);
        tvLoginButton.setClickable(false);
        etLoginPhone.setWatcher(mWatcher);
        etLoginPwd.setWatcher(mWatcher);
        etVerifyCode.setWatcher(mWatcher);
        EditUtil.setEditNotPaste(etLoginPhone);
        EditUtil.setEditNotPaste(etLoginPwd);
        EditUtil.setEditNotPaste(etVerifyCode);
    }


    @SuppressLint("CheckResult")
    @OnClick({R.id.tv_login_button, R.id.tv_forget_pwd, R.id.im_show_pwd,
            R.id.tv_status, R.id.im_verify_code})
    public void onClick(View view) {
        switch (view.getId()) {
            //登录
            case R.id.tv_login_button:
                RxPermissions rxPermissions = new RxPermissions(LoginActivity.this);
                rxPermissions.requestEach(Manifest.permission.READ_PHONE_STATE).subscribe(permission -> {
                    if (permission.granted) {
                        requestLogin();
                    } else if (permission.shouldShowRequestPermissionRationale) {
                        ToastUtils.showShortSafe(getString(R.string.please_open_phone_permission));
                    } else {
                        PermissionUtil.showPermissionDialog(mContext, getString(R.string.phone_permission_name), false);
                    }
                }, throwable -> {
                    if (throwable != null) {
                        ToastUtils.showLongSafe(throwable.getMessage());
                    }
                });
                break;
            //忘记密码
            case R.id.tv_forget_pwd:
//                startNewActivity(TempActivity.class);
                String url = "https://ybs-support.ybm100.com/#/app/forgetPass";
                if (!isAppProd()) {
                    url = "https://ybs-support.test.ybm100.com/#/app/forgetPass";
                }
                ContainerRuntime.INSTANCE.getRouter().open(this, "xyy://crm-app.ybm100.com/crm/web_view?url="+ Uri.encode(url),null);
                SnowGroundUtils.track("Event_ForgetPassword", getZhuGeMap());
                break;
            //显隐密码
            case R.id.im_show_pwd:
                pwdShow();
                break;
            /*case R.id.iv_logo:
                if (!AppNetConfig.FlavorType.PROD.equals(BuildConfig.FLAVOR)) {
                    SharedPrefManager.getInstance().setCurrFlavor("");
                    ToastUtils.showLongSafe("环境重置成功");
                    tvStatus.setText(BuildConfig.FLAVOR);
                }
                break;*/
            case R.id.tv_status:
                if (!AppNetConfig.FlavorType.PROD.equals(BuildConfig.FLAVOR)) {
                    startActivity(SettingUrlActivity.class);
                }
                break;
            case R.id.im_verify_code:
                mPresenter.getVerifyCode();
                progressBar.setVisibility(View.VISIBLE);
                imVerifyCode.setEnabled(false);
                SnowGroundUtils.track("Event_VerifyCode", getZhuGeMap());
                break;
            default:
                break;
        }
    }

    /**
     * 请求登录
     */
    private void requestLogin() {
        String mobile = etLoginPhone.getText().toString().trim();
        String pwd = etLoginPwd.getText().toString().trim();
        String vcode = etVerifyCode.getText().toString().trim();
        HashMap<String, String> map = new HashMap<>();
        try {
            UUID deviceId = new DeviceUuidFactory(mContext).getDeviceUuid();
            map.put("origin", deviceId.toString());
            final String androidId = Settings.Secure.getString(this.getContentResolver(), Settings.Secure.ANDROID_ID);
            map.put("androidId", androidId);
            @SuppressLint("MissingPermission") final String deviceIdByTelManager = ((TelephonyManager) this.getSystemService(Context.TELEPHONY_SERVICE)).getDeviceId();
            map.put("TelManager", deviceIdByTelManager);
            String deviceIdByXYYIO = DeviceUtils.getDeviceId();
            map.put("xyyIO", deviceIdByXYYIO);
        } catch (Exception ignore) {

        }
        SnowGroundUtils.track("deviceId_test", map);
        mPresenter.login(mobile, pwd, "test", vcode, captchaId);
        SnowGroundUtils.track("Event_Login", getZhuGeMap());
    }

    @Override
    public void loginSuccess(UserInfoBean bean) {
        if (bean == null) {
            ToastUtils.showShortSafe("用户数据null");
            return;
        }

        //账号登录时QT记录登录的id
        QtTrackAgent.onProfileSignIn(bean.getSysUserId());

        // 更新一下apm-cly session
        XyyApmCly.getInstance().login(bean.getName(), bean.getSysUserId());

        

        SharedPrefManager.getInstance().setUserInfo(bean);
        SnowGroundUtils.identify(bean.getName(), bean);
        SnowGroundUtils.trackStartUp(bean);
//        startNewActivity(MainActivity.class);
        ContainerRuntime.INSTANCE.getRouter().open(this, "/main", null);
        finish();
    }

    @Override
    public void loginFailure(String errorMsg) {
        mPresenter.getVerifyCode();
        progressBar.setVisibility(View.VISIBLE);
        imVerifyCode.setEnabled(false);
        etVerifyCode.setText("");
        HashMap<String, String> map = getZhuGeMap();
        map.put("errorMsg", errorMsg);
        SnowGroundUtils.track("Event_LoginFailure", map);
    }

    private HashMap<String, String> getZhuGeMap() {
        HashMap<String, String> map = new HashMap<>();
        Editable text = etLoginPhone.getText();
        map.put("account", (TextUtils.isEmpty(text) || text == null || text.length() == 0) ? "" : text.toString());
        map.put("deviceId", DeviceUtils.getXyyDeviceUid());
        return map;
    }

    @Override
    public void getVerifyCodeSuccess(CaptchaBean captchaBean) {
        progressBar.setVisibility(View.GONE);
        setVerifyCode(captchaBean);
        imVerifyCode.setEnabled(true);
        captchaId = captchaBean.getCaptchaId();
    }

    @Override
    public void getVerifyCodeFailure() {
        progressBar.setVisibility(View.GONE);
        imVerifyCode.setEnabled(true);
        imVerifyCode.setBackground(null);
        imVerifyCode.setImageResource(R.drawable.ic_verifycode_failure);
    }

    private void setVerifyCode(CaptchaBean captchaBean) {
        imVerifyCode.setImageBitmap(null);
        imVerifyCode.setBackgroundResource(R.drawable.shape_verifycode_bg_gray);
        imVerifyCode.setImageBitmap(ImageUtils.stringToBitmap(captchaBean.getImg()));
    }

    @Override
    public void onBackPressedSupport() {
        if (getSupportFragmentManager().getBackStackEntryCount() > 1) {
            //如果当前存在fragment>1，当前fragment出栈
            pop();
        } else {
            //如果已经到root fragment了，2秒内点击2次退出
            if (System.currentTimeMillis() - TOUCH_TIME < WAIT_TIME) {
                SnowGroundUtils.flush();
                SharedPrefManager.getInstance().logout();
                AppManager appManager = AppManager.getAppManager();
                appManager.finishAllActivity();
                Process.killProcess(Process.myPid());
                System.exit(0);
            } else {
                TOUCH_TIME = System.currentTimeMillis();
                ToastUtils.showShortSafe(getString(R.string.press_again));
            }
        }
    }

    /**
     * 密码显示方式
     */
    public void pwdShow() {
        imShowPwd.setSelected(!imShowPwd.isSelected());
        int type = InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD;
        if (etLoginPwd.getInputType() == type) {
            etLoginPwd.setInputType(InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD);
            etLoginPwd.setTypeface(Typeface.SANS_SERIF);//设置文本密码格式为半角
            etLoginPwd.setSelection(etLoginPwd.getText().length());//把光标设置到当前文本末尾
        } else {
            etLoginPwd.setInputType(type);
            etLoginPwd.setTypeface(Typeface.SANS_SERIF);
            etLoginPwd.setSelection(etLoginPwd.getText().length());
        }
    }

    private final EditTextWithDel.Watcher mWatcher = new EditTextWithDel.Watcher() {
        @Override
        public void onTextChanged(CharSequence source, int start, int before, int count) {
            if (etLoginPhone.getText() != null && etLoginPhone.getText().length() > 0
                    && etLoginPwd.getText() != null && etLoginPwd.getText().length() > 0
                    && etVerifyCode.getText() != null && etVerifyCode.getText().length() == 4) {
                tvLoginButton.setBackgroundResource(R.drawable.btn_can_click);
                tvLoginButton.setTextColor(ColorUtils.getColor(LoginActivity.this, R.color.white));
                tvLoginButton.setClickable(true);
            } else {
                tvLoginButton.setBackgroundResource(R.drawable.btn_un_click);
                tvLoginButton.setTextColor(ColorUtils.getColor(LoginActivity.this, R.color.text_color_999999));
                tvLoginButton.setClickable(false);
            }
            if (etLoginPwd.getText() != null && etLoginPwd.getText().length() > 0) {
                ViewUtils.setViewVisibility(View.VISIBLE, imShowPwd);
            } else {
                ViewUtils.setViewVisibility(View.INVISIBLE, imShowPwd);
            }
        }
    };

    @Override
    public void showNetError() {

    }
}
